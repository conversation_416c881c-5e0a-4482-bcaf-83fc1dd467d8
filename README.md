# 照片精选系统

一个美观、简洁、高效的网页版照片选片工具，帮助摄影师或工作室向客户交付原始照片（底片），并让客户能够方便地在线挑选需要精修的照片。

## 功能特点

- **三栏布局设计**：左侧信息与操作栏、中间底片展示区、右侧精修选择区
- **套餐管理**：支持多种套餐选择，每个套餐包含不同数量的精修照片
- **照片导入**：支持批量导入JPG格式图片
- **照片预览**：全屏灯箱预览模式，支持缩放和切换
- **相框选择**：为精修照片指定不同的相框样式
- **自动导出**：选片完成后自动创建精修文件夹并复制选中照片
- **增值服务**：支持精修加油包等增值服务

## 技术架构

- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **桌面应用**：Electron
- **数据存储**：JSON文件存储
- **设计风格**：现代简洁，参考macOS App Store设计

## 项目结构

```
├── src/
│   ├── main/                 # Electron主进程
│   │   └── main.js          # 主进程入口文件
│   ├── renderer/            # 渲染进程（前端）
│   │   ├── index.html       # 主页面
│   │   ├── styles/          # 样式文件
│   │   │   └── main.css     # 主样式文件
│   │   └── scripts/         # JavaScript文件
│   │       └── main.js      # 主逻辑文件
│   └── database/            # 数据存储
│       └── models.js        # 数据模型
├── package.json             # 项目配置
├── start.js                 # 开发服务器（用于浏览器预览）
└── README.md               # 项目说明
```

## 开发环境设置

### 方式一：浏览器预览（推荐用于UI开发）

1. 确保已安装 Node.js
2. 启动开发服务器：
   ```bash
   node start.js
   ```
3. 在浏览器中打开：http://localhost:3001

注意：浏览器预览模式使用模拟数据，文件操作功能需要在Electron环境中使用。

### 方式二：Electron桌面应用

1. 安装依赖：
   ```bash
   npm install
   ```

2. 启动应用：
   ```bash
   npm start
   ```

## 使用说明

### 客户端操作流程

1. **选择套餐**：在左侧边栏选择合适的套餐
2. **导入照片**：点击"导入照片文件夹"按钮，选择包含照片的文件夹
3. **浏览底片**：在中间区域浏览所有导入的照片
4. **选择精修**：双击照片将其添加到右侧精修区域
5. **指定相框**：在右侧精修区选中照片，然后在左侧选择相框
6. **确认提交**：完成选择后点击"我选好了，确认提交"

### 管理后台

管理后台功能包括：
- 套餐管理：新增、编辑、删除套餐
- 相框管理：管理可选的相框样式
- 公告管理：编辑活动公告和福利信息
- 增值服务管理：管理精修加油包等服务

## 设计特色

- **现代简洁**：采用卡片式设计，善用留白
- **色彩搭配**：浅灰色、白色背景，蓝色主题色
- **圆角设计**：广泛使用圆角矩形，营造亲和感
- **细微阴影**：为卡片和弹出窗口添加层次感
- **响应式交互**：悬停效果、点击反馈等

## 开发状态

当前已完成：
- ✅ 项目初始化与技术选型
- ✅ 项目结构搭建
- ✅ 数据库设计与模型创建
- ✅ 主界面UI框架搭建
- 🔄 照片导入与展示功能（进行中）

待完成：
- ⏳ 照片选择与管理功能
- ⏳ 图片预览与灯箱功能
- ⏳ 套餐与增值服务功能
- ⏳ 文件导出与重命名功能
- ⏳ 管理后台开发
- ⏳ 样式优化与响应式设计
- ⏳ 测试与优化

## 许可证

ISC License
