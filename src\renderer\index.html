<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片精选系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 主容器 -->
        <div class="main-container">
            <!-- 左侧信息与操作栏 -->
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>套餐选择</h3>
                    <div class="package-selection">
                        <!-- 套餐选项将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>套餐内容</h3>
                    <div class="package-content">
                        <p id="package-description">请选择套餐</p>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>公告</h3>
                    <div class="announcement">
                        <!-- 公告内容将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>增值服务</h3>
                    <div class="addon-services">
                        <!-- 增值服务将通过JavaScript动态加载 -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>相框选择</h3>
                    <div class="frame-selection">
                        <select id="frame-select" disabled>
                            <option value="">请先选择照片</option>
                            <!-- 相框选项将通过JavaScript动态加载 -->
                        </select>
                        <p class="frame-hint">在右侧精修区选择照片后可指定相框</p>
                    </div>
                </div>

                <div class="sidebar-section">
                    <button id="confirm-btn" class="confirm-button" disabled>
                        我选好了，确认提交
                    </button>
                    <button id="help-btn" class="help-button">
                        快捷键帮助
                    </button>
                </div>
            </div>

            <!-- 中间底片展示区 -->
            <div class="gallery-section">
                <div class="gallery-header">
                    <h2>底片 <span id="original-count">(0)</span></h2>
                    <button id="import-btn" class="import-button">
                        导入照片文件夹
                    </button>
                </div>
                <div class="gallery-container" id="original-gallery">
                    <div class="empty-state">
                        <p>点击"导入照片文件夹"开始选片</p>
                    </div>
                </div>
            </div>

            <!-- 右侧精修选择区 -->
            <div class="selected-section">
                <div class="selected-header">
                    <h2>精修 <span id="selected-count">(0/0)</span></h2>
                </div>
                <div class="selected-container" id="selected-gallery">
                    <div class="empty-state">
                        <p>双击底片区的照片添加到精修</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片预览模态框 -->
        <div id="lightbox" class="lightbox">
            <div class="lightbox-content">
                <span class="lightbox-close">&times;</span>
                <img id="lightbox-image" src="" alt="">
                <div class="lightbox-controls">
                    <button id="prev-btn" class="lightbox-btn">‹</button>
                    <button id="next-btn" class="lightbox-btn">›</button>
                </div>
                <div class="lightbox-info">
                    <p>滚轮缩放 | 拖拽移动 | ESC退出 | ←→切换</p>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <script src="scripts/main.js"></script>
</body>
</html>
