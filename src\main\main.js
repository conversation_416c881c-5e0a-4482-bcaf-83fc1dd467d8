const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { Database, PackageDAO, FrameDAO, AnnouncementDAO, AddonServiceDAO } = require('../database/models');

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口会被自动关闭。
let mainWindow;
let database;
let packageDAO, frameDAO, announcementDAO, addonServiceDAO;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'), // 应用图标
    titleBarStyle: 'default',
    show: false // 先不显示，等加载完成后再显示
  });

  // 加载应用的 index.html
  mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

  // 当窗口准备好显示时
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });

  // 开发环境下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// 初始化数据库
async function initDatabase() {
  database = new Database();
  await database.init();

  packageDAO = new PackageDAO(database);
  frameDAO = new FrameDAO(database);
  announcementDAO = new AnnouncementDAO(database);
  addonServiceDAO = new AddonServiceDAO(database);
}

// Electron 会在初始化后并准备创建浏览器窗口时，调用这个函数。
// 部分 API 在 ready 事件触发后才能使用。
app.whenReady().then(async () => {
  await initDatabase();
  createWindow();
});

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC 处理程序
// 选择文件夹
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// 读取文件夹中的图片
ipcMain.handle('read-images', async (event, folderPath) => {
  try {
    const files = fs.readdirSync(folderPath);
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.bmp'].includes(ext);
    });
    
    return imageFiles.map(file => ({
      name: file,
      path: path.join(folderPath, file)
    }));
  } catch (error) {
    console.error('读取图片文件夹失败:', error);
    return [];
  }
});

// 创建精修文件夹并复制选中的图片
ipcMain.handle('export-selected-images', async (event, { sourceFolderPath, selectedImages }) => {
  try {
    const refinedFolderPath = path.join(sourceFolderPath, '精修');

    // 创建精修文件夹
    if (!fs.existsSync(refinedFolderPath)) {
      fs.mkdirSync(refinedFolderPath);
    }

    // 复制选中的图片
    for (const image of selectedImages) {
      const sourcePath = path.join(sourceFolderPath, image.originalName);
      const targetPath = path.join(refinedFolderPath, image.finalName);

      fs.copyFileSync(sourcePath, targetPath);
    }

    return { success: true, path: refinedFolderPath };
  } catch (error) {
    console.error('导出图片失败:', error);
    return { success: false, error: error.message };
  }
});

// 数据库相关的IPC处理程序
// 获取所有套餐
ipcMain.handle('get-packages', async () => {
  try {
    return await packageDAO.getAll();
  } catch (error) {
    console.error('获取套餐失败:', error);
    return [];
  }
});

// 获取所有相框
ipcMain.handle('get-frames', async () => {
  try {
    return await frameDAO.getAll();
  } catch (error) {
    console.error('获取相框失败:', error);
    return [];
  }
});

// 获取所有公告
ipcMain.handle('get-announcements', async () => {
  try {
    return await announcementDAO.getAll();
  } catch (error) {
    console.error('获取公告失败:', error);
    return [];
  }
});

// 获取所有增值服务
ipcMain.handle('get-addon-services', async () => {
  try {
    return await addonServiceDAO.getAll();
  } catch (error) {
    console.error('获取增值服务失败:', error);
    return [];
  }
});
