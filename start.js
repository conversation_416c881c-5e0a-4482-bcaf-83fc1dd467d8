// 简单的启动脚本，用于在没有Electron的情况下测试
const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 3001;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml'
};

const server = http.createServer((req, res) => {
    let filePath = path.join(__dirname, 'src/renderer', req.url === '/' ? 'index.html' : req.url);
    
    // 安全检查
    if (!filePath.startsWith(path.join(__dirname, 'src/renderer'))) {
        res.writeHead(403);
        res.end('Forbidden');
        return;
    }
    
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'text/plain';
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            if (err.code === 'ENOENT') {
                res.writeHead(404);
                res.end('File not found');
            } else {
                res.writeHead(500);
                res.end('Server error');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        }
    });
});

server.listen(port, () => {
    console.log(`开发服务器运行在 http://localhost:${port}`);
    console.log('注意：这只是用于UI预览，文件操作功能需要Electron环境');
});
