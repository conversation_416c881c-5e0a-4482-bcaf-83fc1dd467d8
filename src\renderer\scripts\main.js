// 检查是否在Electron环境中
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;
const ipcRenderer = isElectron ? require('electron').ipcRenderer : null;

// 应用状态
let appState = {
    originalImages: [],
    selectedImages: [],
    currentPackage: null,
    selectedImageForFrame: null,
    sourceFolderPath: null,
    lightboxIndex: 0,
    lightboxImages: [],
    lightboxScale: 1,
    lightboxTranslateX: 0,
    lightboxTranslateY: 0,
    addonServices: {}
};

// DOM 元素
const elements = {
    importBtn: document.getElementById('import-btn'),
    originalGallery: document.getElementById('original-gallery'),
    selectedGallery: document.getElementById('selected-gallery'),
    originalCount: document.getElementById('original-count'),
    selectedCount: document.getElementById('selected-count'),
    packageDescription: document.getElementById('package-description'),
    frameSelect: document.getElementById('frame-select'),
    confirmBtn: document.getElementById('confirm-btn'),
    helpBtn: document.getElementById('help-btn'),
    lightbox: document.getElementById('lightbox'),
    lightboxImage: document.getElementById('lightbox-image'),
    lightboxClose: document.querySelector('.lightbox-close'),
    prevBtn: document.getElementById('prev-btn'),
    nextBtn: document.getElementById('next-btn'),
    loading: document.getElementById('loading'),
    extraRefine: document.getElementById('extra-refine')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    await loadInitialData();
    initializeEventListeners();
    updateUI();
});

// 加载初始数据
async function loadInitialData() {
    try {
        if (ipcRenderer) {
            // Electron环境，从主进程加载数据
            const packages = await ipcRenderer.invoke('get-packages');
            renderPackages(packages);

            const frames = await ipcRenderer.invoke('get-frames');
            renderFrames(frames);

            const announcements = await ipcRenderer.invoke('get-announcements');
            renderAnnouncements(announcements);

            const addonServices = await ipcRenderer.invoke('get-addon-services');
            renderAddonServices(addonServices);
        } else {
            // 浏览器环境，使用模拟数据
            const mockData = getMockData();
            renderPackages(mockData.packages);
            renderFrames(mockData.frames);
            renderAnnouncements(mockData.announcements);
            renderAddonServices(mockData.addon_services);
        }
    } catch (error) {
        console.error('加载初始数据失败:', error);
    }
}

// 获取模拟数据（用于浏览器预览）
function getMockData() {
    return {
        packages: [
            { id: 1, name: '￥1099 套餐', price: 1099, refine_count: 30, description: '基础套餐，包含30张精修照片' },
            { id: 2, name: '￥1599 套餐', price: 1599, refine_count: 50, description: '标准套餐，包含50张精修照片' },
            { id: 3, name: '￥2099 套餐', price: 2099, refine_count: 80, description: '豪华套餐，包含80张精修照片' }
        ],
        frames: [
            { id: 1, name: '简约白框', description: '简洁的白色相框' },
            { id: 2, name: '复古金框', description: '复古风格的金色相框' },
            { id: 3, name: '现代黑框', description: '现代简约的黑色相框' },
            { id: 4, name: '木质相框', description: '天然木质相框' }
        ],
        announcements: [
            { id: 1, title: '好评活动', content: '五星好评送精修加油包！', type: 'promotion', is_active: true },
            { id: 2, title: '续订福利', content: '老客户续订享受9折优惠', type: 'benefit', is_active: true }
        ],
        addon_services: [
            { id: 1, name: '精修加油包', price: 20, unit: '张', description: '额外的精修照片服务', is_active: true }
        ]
    };
}

// 渲染套餐选项
function renderPackages(packages) {
    const packageSelection = document.querySelector('.package-selection');
    packageSelection.innerHTML = '';

    packages.forEach((pkg, index) => {
        const div = document.createElement('div');
        div.className = 'package-option';
        div.innerHTML = `
            <input type="radio" id="package${pkg.id}" name="package" value="${pkg.price}" data-count="${pkg.refine_count}" data-id="${pkg.id}">
            <label for="package${pkg.id}">${pkg.name}</label>
        `;
        packageSelection.appendChild(div);
    });

    // 重新绑定事件监听器
    document.querySelectorAll('input[name="package"]').forEach(radio => {
        radio.addEventListener('change', handlePackageChange);
    });
}

// 渲染相框选项
function renderFrames(frames) {
    const frameSelect = elements.frameSelect;
    // 保留第一个默认选项
    frameSelect.innerHTML = '<option value="">请先选择照片</option>';

    frames.forEach(frame => {
        const option = document.createElement('option');
        option.value = frame.name;
        option.textContent = frame.name;
        frameSelect.appendChild(option);
    });
}

// 渲染公告
function renderAnnouncements(announcements) {
    const announcementContainer = document.querySelector('.announcement');
    announcementContainer.innerHTML = '';

    announcements.forEach(announcement => {
        const div = document.createElement('div');
        div.className = 'announcement-item';
        div.innerHTML = `
            <h4>${announcement.title}</h4>
            <p>${announcement.content}</p>
        `;
        announcementContainer.appendChild(div);
    });
}

// 渲染增值服务
function renderAddonServices(services) {
    const addonContainer = document.querySelector('.addon-services');
    addonContainer.innerHTML = '';

    services.forEach(service => {
        const div = document.createElement('div');
        div.className = 'addon-item';
        div.innerHTML = `
            <label>${service.name}：</label>
            <input type="number" id="addon-${service.id}" min="0" value="0" class="addon-input" data-price="${service.price}" data-service-id="${service.id}">
            <span>${service.unit} (￥${service.price}/${service.unit})</span>
        `;
        addonContainer.appendChild(div);

        // 绑定变更事件
        const input = div.querySelector(`#addon-${service.id}`);
        input.addEventListener('change', updateAddonServices);
        input.addEventListener('input', updateAddonServices);
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 导入照片按钮
    elements.importBtn.addEventListener('click', importPhotos);
    
    // 套餐选择
    document.querySelectorAll('input[name="package"]').forEach(radio => {
        radio.addEventListener('change', handlePackageChange);
    });
    
    // 相框选择
    elements.frameSelect.addEventListener('change', handleFrameChange);
    
    // 确认提交按钮
    elements.confirmBtn.addEventListener('click', handleConfirmSubmit);

    // 帮助按钮
    elements.helpBtn.addEventListener('click', showHelp);
    
    // 灯箱控制
    elements.lightboxClose.addEventListener('click', closeLightbox);
    elements.prevBtn.addEventListener('click', showPrevImage);
    elements.nextBtn.addEventListener('click', showNextImage);
    
    // 键盘事件
    document.addEventListener('keydown', handleKeyDown);
    
    // 点击灯箱背景关闭
    elements.lightbox.addEventListener('click', (e) => {
        if (e.target === elements.lightbox) {
            closeLightbox();
        }
    });

    // 灯箱鼠标滚轮缩放
    elements.lightbox.addEventListener('wheel', handleLightboxWheel);

    // 灯箱图片拖拽
    let isDragging = false;
    let dragStartX = 0;
    let dragStartY = 0;

    elements.lightboxImage.addEventListener('mousedown', (e) => {
        if (appState.lightboxScale > 1) {
            isDragging = true;
            dragStartX = e.clientX - appState.lightboxTranslateX;
            dragStartY = e.clientY - appState.lightboxTranslateY;
            elements.lightboxImage.style.cursor = 'grabbing';
            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', (e) => {
        if (isDragging) {
            appState.lightboxTranslateX = e.clientX - dragStartX;
            appState.lightboxTranslateY = e.clientY - dragStartY;
            updateLightboxTransform();
        }
    });

    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            elements.lightboxImage.style.cursor = appState.lightboxScale > 1 ? 'grab' : 'default';
        }
    });
}

// 导入照片
async function importPhotos() {
    if (!ipcRenderer) {
        // 浏览器预览模式，使用模拟数据
        loadMockImages();
        return;
    }

    try {
        showLoading(true);

        const folderPath = await ipcRenderer.invoke('select-folder');
        if (!folderPath) {
            showLoading(false);
            return;
        }

        appState.sourceFolderPath = folderPath;
        const images = await ipcRenderer.invoke('read-images', folderPath);

        appState.originalImages = images.map(img => ({
            name: img.name,
            path: img.path,
            id: generateId()
        }));

        renderOriginalGallery();
        updateUI();

    } catch (error) {
        console.error('导入照片失败:', error);
        alert('导入照片失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 加载模拟图片（用于浏览器预览）
function loadMockImages() {
    showLoading(true);

    try {
        // 模拟一些图片数据
        const mockImages = [];
        for (let i = 1; i <= 24; i++) {
            mockImages.push({
                name: `IMG_${String(i).padStart(4, '0')}.jpg`,
                path: `https://picsum.photos/300/200?random=${i}`, // 使用Lorem Picsum提供的随机图片
                id: generateId()
            });
        }

        appState.originalImages = mockImages;
        appState.sourceFolderPath = '/mock/photos'; // 模拟路径

        setTimeout(() => {
            renderOriginalGallery();
            updateUI();
            showLoading(false);
            showToast(`已加载 ${mockImages.length} 张模拟照片`, 'info');
        }, 1000); // 模拟加载时间

    } catch (error) {
        console.error('加载模拟图片失败:', error);
        showLoading(false);
        showToast('加载图片失败，请重试', 'error');
    }
}

// 渲染原始照片画廊
function renderOriginalGallery() {
    if (appState.originalImages.length === 0) {
        elements.originalGallery.innerHTML = '<div class="empty-state"><p>点击"导入照片文件夹"开始选片</p></div>';
        return;
    }

    elements.originalGallery.innerHTML = '';

    appState.originalImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'original');

        // 检查是否已在精修区
        const isInSelected = appState.selectedImages.some(selected => selected.id === image.id);
        if (isInSelected) {
            thumbnail.classList.add('in-selected');
        }

        elements.originalGallery.appendChild(thumbnail);
    });
}

// 渲染选中照片画廊
function renderSelectedGallery() {
    if (appState.selectedImages.length === 0) {
        elements.selectedGallery.innerHTML = '<div class="empty-state"><p>双击底片区的照片添加到精修</p></div>';
        return;
    }
    
    elements.selectedGallery.innerHTML = '';
    
    appState.selectedImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'selected');
        elements.selectedGallery.appendChild(thumbnail);
    });
}

// 创建图片缩略图
function createImageThumbnail(image, index, type) {
    const div = document.createElement('div');
    div.className = 'image-thumbnail';
    div.dataset.imageId = image.id;
    div.dataset.type = type;

    const img = document.createElement('img');
    // 使用懒加载和占位符
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=';
    img.dataset.src = ipcRenderer ? `file://${image.path}` : image.path;
    img.alt = image.name;
    img.loading = 'lazy';

    // 实现懒加载
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.onload = () => {
                    img.style.opacity = '1';
                };
                observer.unobserve(img);
            }
        });
    }, { threshold: 0.1 });

    observer.observe(img);
    img.style.opacity = '0.5';
    img.style.transition = 'opacity 0.3s ease';

    const info = document.createElement('div');
    info.className = 'image-info';
    info.innerHTML = `
        <div class="image-name">${truncateFileName(image.name, 15)}</div>
        ${image.frame ? `<div class="frame-info">相框: ${image.frame}</div>` : ''}
    `;

    div.appendChild(img);
    div.appendChild(info);

    // 防抖处理点击事件
    let clickTimeout;
    div.addEventListener('click', () => {
        clearTimeout(clickTimeout);
        clickTimeout = setTimeout(() => {
            if (type === 'selected') {
                selectImageForFrame(image);
            }
            showLightbox(image, type);
        }, 200);
    });

    // 双击选择/取消
    div.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        clearTimeout(clickTimeout);
        if (type === 'original') {
            addToSelected(image);
        } else {
            removeFromSelected(image);
        }
    });

    return div;
}

// 添加到精修
function addToSelected(image) {
    // 检查是否已经选中
    if (appState.selectedImages.find(img => img.id === image.id)) {
        return;
    }

    // 检查套餐限制
    const maxCount = getCurrentPackageLimit();
    if (maxCount && appState.selectedImages.length >= maxCount) {
        alert(`当前套餐最多只能选择 ${maxCount} 张照片`);
        return;
    }

    appState.selectedImages.push({
        ...image,
        frame: null
    });

    // 重新渲染两个画廊以更新视觉状态
    renderSelectedGallery();
    renderOriginalGallery();
    updateUI();

    // 显示成功提示
    showToast(`已添加 ${image.name} 到精修区`);
}

// 从精修中移除
function removeFromSelected(image) {
    appState.selectedImages = appState.selectedImages.filter(img => img.id !== image.id);

    // 如果移除的是当前选中的图片，清除相框选择
    if (appState.selectedImageForFrame && appState.selectedImageForFrame.id === image.id) {
        appState.selectedImageForFrame = null;
        elements.frameSelect.disabled = true;
        elements.frameSelect.value = '';
    }

    // 重新渲染两个画廊以更新视觉状态
    renderSelectedGallery();
    renderOriginalGallery();
    updateUI();

    // 显示成功提示
    showToast(`已从精修区移除 ${image.name}`);
}

// 选择图片用于指定相框
function selectImageForFrame(image) {
    // 移除之前的选中状态
    document.querySelectorAll('.image-thumbnail.selected').forEach(el => {
        el.classList.remove('selected');
    });
    
    // 添加选中状态
    const thumbnail = document.querySelector(`[data-image-id="${image.id}"][data-type="selected"]`);
    if (thumbnail) {
        thumbnail.classList.add('selected');
    }
    
    appState.selectedImageForFrame = image;
    elements.frameSelect.disabled = false;
    elements.frameSelect.value = image.frame || '';
}

// 处理套餐变更
function handlePackageChange(e) {
    const radio = e.target;
    const price = radio.value;
    const count = radio.dataset.count;
    const packageId = radio.dataset.id;

    appState.currentPackage = {
        id: parseInt(packageId),
        price: parseInt(price),
        count: parseInt(count)
    };

    // 更新价格显示
    updatePriceDisplay();

    // 如果当前选中的照片超过新套餐限制，提示用户
    if (appState.selectedImages.length > parseInt(count)) {
        const excess = appState.selectedImages.length - parseInt(count);
        if (confirm(`当前已选择 ${appState.selectedImages.length} 张照片，超出新套餐限制 ${excess} 张。是否自动移除多余的照片？`)) {
            // 保留前N张照片
            const removedImages = appState.selectedImages.slice(parseInt(count));
            appState.selectedImages = appState.selectedImages.slice(0, parseInt(count));

            // 清除被移除照片的相框选择
            if (appState.selectedImageForFrame && removedImages.some(img => img.id === appState.selectedImageForFrame.id)) {
                appState.selectedImageForFrame = null;
                elements.frameSelect.disabled = true;
                elements.frameSelect.value = '';
            }

            renderSelectedGallery();
            renderOriginalGallery();
            showToast(`已自动移除 ${excess} 张超出限制的照片`, 'warning');
        }
    }

    updateUI();
}

// 更新增值服务
function updateAddonServices() {
    const addonInputs = document.querySelectorAll('.addon-input');
    appState.addonServices = {};

    addonInputs.forEach(input => {
        const serviceId = input.dataset.serviceId;
        const quantity = parseInt(input.value) || 0;
        const price = parseInt(input.dataset.price) || 0;

        if (quantity > 0) {
            appState.addonServices[serviceId] = {
                quantity: quantity,
                price: price,
                total: quantity * price
            };
        }
    });

    updatePriceDisplay();
}

// 更新价格显示
function updatePriceDisplay() {
    let totalPrice = 0;

    // 套餐价格
    if (appState.currentPackage) {
        totalPrice += appState.currentPackage.price;
    }

    // 增值服务价格
    Object.values(appState.addonServices).forEach(service => {
        totalPrice += service.total;
    });

    // 更新套餐内容显示
    if (appState.currentPackage) {
        let description = `（￥${appState.currentPackage.price}套餐，${appState.currentPackage.count}张精修）`;

        const addonTotal = totalPrice - appState.currentPackage.price;
        if (addonTotal > 0) {
            description += `\n增值服务：￥${addonTotal}`;
            description += `\n总计：￥${totalPrice}`;
        }

        elements.packageDescription.innerHTML = description.replace(/\n/g, '<br>');
    }
}

// 处理相框变更
function handleFrameChange(e) {
    if (!appState.selectedImageForFrame) return;
    
    const frameValue = e.target.value;
    const imageIndex = appState.selectedImages.findIndex(img => img.id === appState.selectedImageForFrame.id);
    
    if (imageIndex !== -1) {
        appState.selectedImages[imageIndex].frame = frameValue || null;
        renderSelectedGallery();
        
        // 重新选中该图片
        setTimeout(() => {
            selectImageForFrame(appState.selectedImages[imageIndex]);
        }, 100);
    }
}

// 处理确认提交
async function handleConfirmSubmit() {
    if (!appState.sourceFolderPath || appState.selectedImages.length === 0) {
        alert('请先导入照片并选择要精修的照片');
        return;
    }

    if (!appState.currentPackage) {
        alert('请选择套餐');
        return;
    }

    try {
        showLoading(true);

        // 准备导出数据
        const selectedImages = appState.selectedImages.map(img => ({
            originalName: img.name,
            finalName: img.frame ? `${getFileNameWithoutExt(img.name)}_${img.frame}.${getFileExtension(img.name)}` : img.name
        }));

        if (ipcRenderer) {
            // 准备订单信息
            const orderInfo = {
                package: appState.currentPackage,
                addonServices: appState.addonServices,
                totalImages: appState.selectedImages.length,
                totalPrice: calculateTotalPrice()
            };

            // Electron环境
            const result = await ipcRenderer.invoke('export-selected-images', {
                sourceFolderPath: appState.sourceFolderPath,
                selectedImages,
                orderInfo
            });

            if (result.success) {
                alert(`选片完成！已在原文件夹内生成"精修"文件夹。\n路径：${result.path}`);
            } else {
                alert(`导出失败：${result.error}`);
            }
        } else {
            // 浏览器环境，显示选择结果
            let resultText = '选片结果预览：\n\n';
            resultText += `套餐：${appState.currentPackage.price}元套餐 (${appState.currentPackage.count}张精修)\n`;
            resultText += `已选择：${appState.selectedImages.length}张照片\n`;

            // 增值服务信息
            const addonServices = Object.values(appState.addonServices);
            if (addonServices.length > 0) {
                resultText += '\n增值服务：\n';
                addonServices.forEach(service => {
                    resultText += `- 精修加油包：${service.quantity}张 (￥${service.total})\n`;
                });
            }

            // 总价计算
            let totalPrice = appState.currentPackage.price;
            addonServices.forEach(service => totalPrice += service.total);
            resultText += `\n总价：￥${totalPrice}\n\n`;

            resultText += '选中的照片：\n';
            selectedImages.forEach((img, index) => {
                resultText += `${index + 1}. ${img.finalName}\n`;
            });

            resultText += '\n注意：实际文件导出功能需要在Electron桌面应用中使用。';
            alert(resultText);
        }

    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 显示灯箱
function showLightbox(image, type) {
    appState.lightboxImages = type === 'original' ? appState.originalImages : appState.selectedImages;
    appState.lightboxIndex = appState.lightboxImages.findIndex(img => img.id === image.id);

    // 重置缩放和位移
    appState.lightboxScale = 1;
    appState.lightboxTranslateX = 0;
    appState.lightboxTranslateY = 0;

    updateLightboxImage();
    elements.lightbox.style.display = 'block';
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

// 更新灯箱图片
function updateLightboxImage() {
    const image = appState.lightboxImages[appState.lightboxIndex];
    if (image) {
        elements.lightboxImage.src = ipcRenderer ? `file://${image.path}` : image.path;

        // 重置缩放和位移
        appState.lightboxScale = 1;
        appState.lightboxTranslateX = 0;
        appState.lightboxTranslateY = 0;
        updateLightboxTransform();
    }
}

// 关闭灯箱
function closeLightbox() {
    elements.lightbox.style.display = 'none';
    document.body.style.overflow = ''; // 恢复背景滚动

    // 重置状态
    appState.lightboxScale = 1;
    appState.lightboxTranslateX = 0;
    appState.lightboxTranslateY = 0;
}

// 显示上一张图片
function showPrevImage() {
    if (appState.lightboxIndex > 0) {
        appState.lightboxIndex--;
        updateLightboxImage();
    }
}

// 显示下一张图片
function showNextImage() {
    if (appState.lightboxIndex < appState.lightboxImages.length - 1) {
        appState.lightboxIndex++;
        updateLightboxImage();
    }
}

// 处理键盘事件
function handleKeyDown(e) {
    // 灯箱快捷键
    if (elements.lightbox.style.display === 'block') {
        switch (e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                showPrevImage();
                break;
            case 'ArrowRight':
                showNextImage();
                break;
            case '+':
            case '=':
                e.preventDefault();
                handleLightboxWheel({ deltaY: -100, clientX: window.innerWidth / 2, clientY: window.innerHeight / 2, preventDefault: () => {} });
                break;
            case '-':
                e.preventDefault();
                handleLightboxWheel({ deltaY: 100, clientX: window.innerWidth / 2, clientY: window.innerHeight / 2, preventDefault: () => {} });
                break;
        }
        return;
    }

    // 全局快捷键
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'i':
                e.preventDefault();
                importPhotos();
                break;
            case 'Enter':
                e.preventDefault();
                if (!elements.confirmBtn.disabled) {
                    handleConfirmSubmit();
                }
                break;
        }
    }

    // 数字键选择套餐
    if (e.key >= '1' && e.key <= '9') {
        const packageRadios = document.querySelectorAll('input[name="package"]');
        const index = parseInt(e.key) - 1;
        if (packageRadios[index]) {
            packageRadios[index].checked = true;
            packageRadios[index].dispatchEvent(new Event('change'));
        }
    }
}

// 更新UI状态
function updateUI() {
    // 更新计数器
    elements.originalCount.textContent = `(${appState.originalImages.length})`;

    const maxCount = getCurrentPackageLimit();
    const selectedCount = appState.selectedImages.length;

    if (maxCount) {
        elements.selectedCount.textContent = `(${selectedCount}/${maxCount})`;
        // 根据选择状态改变颜色
        if (selectedCount > maxCount) {
            elements.selectedCount.style.color = '#dc3545'; // 红色：超出限制
        } else if (selectedCount === maxCount) {
            elements.selectedCount.style.color = '#28a745'; // 绿色：刚好满足
        } else {
            elements.selectedCount.style.color = '#333'; // 默认颜色
        }
    } else {
        elements.selectedCount.textContent = `(${selectedCount}/0)`;
        elements.selectedCount.style.color = '#6c757d'; // 灰色：未选择套餐
    }

    // 更新确认按钮状态
    const canConfirm = appState.currentPackage &&
                      appState.selectedImages.length > 0 &&
                      appState.selectedImages.length <= getCurrentPackageLimit() &&
                      appState.sourceFolderPath;

    elements.confirmBtn.disabled = !canConfirm;

    // 更新按钮文本提示
    if (!appState.currentPackage) {
        elements.confirmBtn.textContent = '请先选择套餐';
    } else if (appState.selectedImages.length === 0) {
        elements.confirmBtn.textContent = '请选择要精修的照片';
    } else if (appState.selectedImages.length > getCurrentPackageLimit()) {
        elements.confirmBtn.textContent = `超出套餐限制 (${appState.selectedImages.length}/${getCurrentPackageLimit()})`;
    } else if (!appState.sourceFolderPath) {
        elements.confirmBtn.textContent = '请先导入照片';
    } else {
        elements.confirmBtn.textContent = '我选好了，确认提交';
    }
}

// 获取当前套餐限制
function getCurrentPackageLimit() {
    return appState.currentPackage ? appState.currentPackage.count : 0;
}

// 计算总价
function calculateTotalPrice() {
    let totalPrice = 0;

    // 套餐价格
    if (appState.currentPackage) {
        totalPrice += appState.currentPackage.price;
    }

    // 增值服务价格
    Object.values(appState.addonServices).forEach(service => {
        totalPrice += service.total;
    });

    return totalPrice;
}

// 显示/隐藏加载状态
function showLoading(show) {
    elements.loading.style.display = show ? 'flex' : 'none';
}

// 工具函数
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function getFileNameWithoutExt(filename) {
    return filename.substring(0, filename.lastIndexOf('.'));
}

function getFileExtension(filename) {
    return filename.substring(filename.lastIndexOf('.') + 1);
}

// 显示toast提示
function showToast(message, type = 'success') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 处理灯箱滚轮事件
function handleLightboxWheel(e) {
    e.preventDefault();

    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newScale = Math.max(0.5, Math.min(3, appState.lightboxScale + delta));

    if (newScale !== appState.lightboxScale) {
        // 计算缩放中心点
        const rect = elements.lightboxImage.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const mouseX = e.clientX;
        const mouseY = e.clientY;

        // 调整位移以保持鼠标位置为缩放中心
        const scaleRatio = newScale / appState.lightboxScale;
        appState.lightboxTranslateX = mouseX - (mouseX - appState.lightboxTranslateX) * scaleRatio;
        appState.lightboxTranslateY = mouseY - (mouseY - appState.lightboxTranslateY) * scaleRatio;

        appState.lightboxScale = newScale;
        updateLightboxTransform();

        // 更新鼠标样式
        elements.lightboxImage.style.cursor = appState.lightboxScale > 1 ? 'grab' : 'default';
    }
}

// 更新灯箱图片变换
function updateLightboxTransform() {
    const transform = `translate(${appState.lightboxTranslateX}px, ${appState.lightboxTranslateY}px) scale(${appState.lightboxScale})`;
    elements.lightboxImage.style.transform = transform;
}

// 截断文件名
function truncateFileName(fileName, maxLength) {
    if (fileName.length <= maxLength) return fileName;

    const ext = fileName.substring(fileName.lastIndexOf('.'));
    const name = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = name.substring(0, maxLength - ext.length - 3) + '...';

    return truncatedName + ext;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 性能监控
function measurePerformance(name, func) {
    return async function(...args) {
        const start = performance.now();
        const result = await func.apply(this, args);
        const end = performance.now();
        console.log(`${name} 执行时间: ${(end - start).toFixed(2)}ms`);
        return result;
    };
}

// 内存清理
function cleanupResources() {
    // 清理未使用的图片引用
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (!img.parentNode) {
            img.src = '';
        }
    });

    // 强制垃圾回收（如果可用）
    if (window.gc) {
        window.gc();
    }
}

// 显示帮助信息
function showHelp() {
    const helpText = `
照片精选系统 - 快捷键帮助

基本操作：
• 单击照片：预览照片
• 双击照片：添加/移除精修
• 数字键 1-9：快速选择套餐

图片预览：
• ESC：关闭预览
• ←→：切换上下张
• 滚轮：缩放图片
• +/-：放大/缩小
• 拖拽：移动图片

全局快捷键：
• Ctrl+I：导入照片
• Ctrl+Enter：确认提交

操作流程：
1. 选择套餐
2. 导入照片文件夹
3. 双击选择要精修的照片
4. 指定相框（可选）
5. 确认提交

注意：浏览器预览模式仅供界面演示，
完整功能需要在Electron桌面应用中使用。
    `.trim();

    alert(helpText);
}
