const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 应用状态
let appState = {
    originalImages: [],
    selectedImages: [],
    currentPackage: null,
    selectedImageForFrame: null,
    sourceFolderPath: null,
    lightboxIndex: 0,
    lightboxImages: []
};

// DOM 元素
const elements = {
    importBtn: document.getElementById('import-btn'),
    originalGallery: document.getElementById('original-gallery'),
    selectedGallery: document.getElementById('selected-gallery'),
    originalCount: document.getElementById('original-count'),
    selectedCount: document.getElementById('selected-count'),
    packageDescription: document.getElementById('package-description'),
    frameSelect: document.getElementById('frame-select'),
    confirmBtn: document.getElementById('confirm-btn'),
    lightbox: document.getElementById('lightbox'),
    lightboxImage: document.getElementById('lightbox-image'),
    lightboxClose: document.querySelector('.lightbox-close'),
    prevBtn: document.getElementById('prev-btn'),
    nextBtn: document.getElementById('next-btn'),
    loading: document.getElementById('loading'),
    extraRefine: document.getElementById('extra-refine')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    await loadInitialData();
    initializeEventListeners();
    updateUI();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 导入照片按钮
    elements.importBtn.addEventListener('click', importPhotos);
    
    // 套餐选择
    document.querySelectorAll('input[name="package"]').forEach(radio => {
        radio.addEventListener('change', handlePackageChange);
    });
    
    // 相框选择
    elements.frameSelect.addEventListener('change', handleFrameChange);
    
    // 确认提交按钮
    elements.confirmBtn.addEventListener('click', handleConfirmSubmit);
    
    // 灯箱控制
    elements.lightboxClose.addEventListener('click', closeLightbox);
    elements.prevBtn.addEventListener('click', showPrevImage);
    elements.nextBtn.addEventListener('click', showNextImage);
    
    // 键盘事件
    document.addEventListener('keydown', handleKeyDown);
    
    // 点击灯箱背景关闭
    elements.lightbox.addEventListener('click', (e) => {
        if (e.target === elements.lightbox) {
            closeLightbox();
        }
    });
}

// 导入照片
async function importPhotos() {
    try {
        showLoading(true);
        
        const folderPath = await ipcRenderer.invoke('select-folder');
        if (!folderPath) {
            showLoading(false);
            return;
        }
        
        appState.sourceFolderPath = folderPath;
        const images = await ipcRenderer.invoke('read-images', folderPath);
        
        appState.originalImages = images.map(img => ({
            name: img.name,
            path: img.path,
            id: generateId()
        }));
        
        renderOriginalGallery();
        updateUI();
        
    } catch (error) {
        console.error('导入照片失败:', error);
        alert('导入照片失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 渲染原始照片画廊
function renderOriginalGallery() {
    if (appState.originalImages.length === 0) {
        elements.originalGallery.innerHTML = '<div class="empty-state"><p>点击"导入照片文件夹"开始选片</p></div>';
        return;
    }
    
    elements.originalGallery.innerHTML = '';
    
    appState.originalImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'original');
        elements.originalGallery.appendChild(thumbnail);
    });
}

// 渲染选中照片画廊
function renderSelectedGallery() {
    if (appState.selectedImages.length === 0) {
        elements.selectedGallery.innerHTML = '<div class="empty-state"><p>双击底片区的照片添加到精修</p></div>';
        return;
    }
    
    elements.selectedGallery.innerHTML = '';
    
    appState.selectedImages.forEach((image, index) => {
        const thumbnail = createImageThumbnail(image, index, 'selected');
        elements.selectedGallery.appendChild(thumbnail);
    });
}

// 创建图片缩略图
function createImageThumbnail(image, index, type) {
    const div = document.createElement('div');
    div.className = 'image-thumbnail';
    div.dataset.imageId = image.id;
    div.dataset.type = type;
    
    const img = document.createElement('img');
    img.src = `file://${image.path}`;
    img.alt = image.name;
    img.loading = 'lazy';
    
    const info = document.createElement('div');
    info.className = 'image-info';
    info.innerHTML = `
        <div class="image-name">${image.name}</div>
        ${image.frame ? `<div class="frame-info">相框: ${image.frame}</div>` : ''}
    `;
    
    div.appendChild(img);
    div.appendChild(info);
    
    // 单击预览
    div.addEventListener('click', () => {
        if (type === 'selected') {
            selectImageForFrame(image);
        }
        showLightbox(image, type);
    });
    
    // 双击选择/取消
    div.addEventListener('dblclick', (e) => {
        e.stopPropagation();
        if (type === 'original') {
            addToSelected(image);
        } else {
            removeFromSelected(image);
        }
    });
    
    return div;
}

// 添加到精修
function addToSelected(image) {
    // 检查是否已经选中
    if (appState.selectedImages.find(img => img.id === image.id)) {
        return;
    }
    
    // 检查套餐限制
    const maxCount = getCurrentPackageLimit();
    if (maxCount && appState.selectedImages.length >= maxCount) {
        alert(`当前套餐最多只能选择 ${maxCount} 张照片`);
        return;
    }
    
    appState.selectedImages.push({
        ...image,
        frame: null
    });
    
    renderSelectedGallery();
    updateUI();
}

// 从精修中移除
function removeFromSelected(image) {
    appState.selectedImages = appState.selectedImages.filter(img => img.id !== image.id);
    
    // 如果移除的是当前选中的图片，清除相框选择
    if (appState.selectedImageForFrame && appState.selectedImageForFrame.id === image.id) {
        appState.selectedImageForFrame = null;
        elements.frameSelect.disabled = true;
        elements.frameSelect.value = '';
    }
    
    renderSelectedGallery();
    updateUI();
}

// 选择图片用于指定相框
function selectImageForFrame(image) {
    // 移除之前的选中状态
    document.querySelectorAll('.image-thumbnail.selected').forEach(el => {
        el.classList.remove('selected');
    });
    
    // 添加选中状态
    const thumbnail = document.querySelector(`[data-image-id="${image.id}"][data-type="selected"]`);
    if (thumbnail) {
        thumbnail.classList.add('selected');
    }
    
    appState.selectedImageForFrame = image;
    elements.frameSelect.disabled = false;
    elements.frameSelect.value = image.frame || '';
}

// 处理套餐变更
function handlePackageChange(e) {
    const radio = e.target;
    const price = radio.value;
    const count = radio.dataset.count;
    
    appState.currentPackage = {
        price: parseInt(price),
        count: parseInt(count)
    };
    
    elements.packageDescription.textContent = `（￥${price}套餐，${count}张精修）`;
    
    // 如果当前选中的照片超过新套餐限制，提示用户
    if (appState.selectedImages.length > parseInt(count)) {
        const excess = appState.selectedImages.length - parseInt(count);
        if (confirm(`当前已选择 ${appState.selectedImages.length} 张照片，超出新套餐限制 ${excess} 张。是否自动移除多余的照片？`)) {
            appState.selectedImages = appState.selectedImages.slice(0, parseInt(count));
            renderSelectedGallery();
        }
    }
    
    updateUI();
}

// 处理相框变更
function handleFrameChange(e) {
    if (!appState.selectedImageForFrame) return;
    
    const frameValue = e.target.value;
    const imageIndex = appState.selectedImages.findIndex(img => img.id === appState.selectedImageForFrame.id);
    
    if (imageIndex !== -1) {
        appState.selectedImages[imageIndex].frame = frameValue || null;
        renderSelectedGallery();
        
        // 重新选中该图片
        setTimeout(() => {
            selectImageForFrame(appState.selectedImages[imageIndex]);
        }, 100);
    }
}

// 处理确认提交
async function handleConfirmSubmit() {
    if (!appState.sourceFolderPath || appState.selectedImages.length === 0) {
        alert('请先导入照片并选择要精修的照片');
        return;
    }
    
    if (!appState.currentPackage) {
        alert('请选择套餐');
        return;
    }
    
    try {
        showLoading(true);
        
        // 准备导出数据
        const selectedImages = appState.selectedImages.map(img => ({
            originalName: img.name,
            finalName: img.frame ? `${getFileNameWithoutExt(img.name)}_${img.frame}.${getFileExtension(img.name)}` : img.name
        }));
        
        const result = await ipcRenderer.invoke('export-selected-images', {
            sourceFolderPath: appState.sourceFolderPath,
            selectedImages
        });
        
        if (result.success) {
            alert(`选片完成！已在原文件夹内生成"精修"文件夹。\n路径：${result.path}`);
        } else {
            alert(`导出失败：${result.error}`);
        }
        
    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请重试');
    } finally {
        showLoading(false);
    }
}

// 显示灯箱
function showLightbox(image, type) {
    appState.lightboxImages = type === 'original' ? appState.originalImages : appState.selectedImages;
    appState.lightboxIndex = appState.lightboxImages.findIndex(img => img.id === image.id);
    
    updateLightboxImage();
    elements.lightbox.style.display = 'block';
}

// 更新灯箱图片
function updateLightboxImage() {
    const image = appState.lightboxImages[appState.lightboxIndex];
    if (image) {
        elements.lightboxImage.src = `file://${image.path}`;
    }
}

// 关闭灯箱
function closeLightbox() {
    elements.lightbox.style.display = 'none';
}

// 显示上一张图片
function showPrevImage() {
    if (appState.lightboxIndex > 0) {
        appState.lightboxIndex--;
        updateLightboxImage();
    }
}

// 显示下一张图片
function showNextImage() {
    if (appState.lightboxIndex < appState.lightboxImages.length - 1) {
        appState.lightboxIndex++;
        updateLightboxImage();
    }
}

// 处理键盘事件
function handleKeyDown(e) {
    if (elements.lightbox.style.display === 'block') {
        switch (e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                showPrevImage();
                break;
            case 'ArrowRight':
                showNextImage();
                break;
        }
    }
}

// 更新UI状态
function updateUI() {
    // 更新计数器
    elements.originalCount.textContent = `(${appState.originalImages.length})`;
    
    const maxCount = getCurrentPackageLimit();
    const selectedCount = appState.selectedImages.length;
    
    if (maxCount) {
        elements.selectedCount.textContent = `(${selectedCount}/${maxCount})`;
        elements.selectedCount.style.color = selectedCount > maxCount ? '#dc3545' : '#333';
    } else {
        elements.selectedCount.textContent = `(${selectedCount}/0)`;
    }
    
    // 更新确认按钮状态
    const canConfirm = appState.currentPackage && 
                      appState.selectedImages.length > 0 && 
                      appState.selectedImages.length <= getCurrentPackageLimit();
    
    elements.confirmBtn.disabled = !canConfirm;
}

// 获取当前套餐限制
function getCurrentPackageLimit() {
    return appState.currentPackage ? appState.currentPackage.count : 0;
}

// 显示/隐藏加载状态
function showLoading(show) {
    elements.loading.style.display = show ? 'flex' : 'none';
}

// 工具函数
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

function getFileNameWithoutExt(filename) {
    return filename.substring(0, filename.lastIndexOf('.'));
}

function getFileExtension(filename) {
    return filename.substring(filename.lastIndexOf('.') + 1);
}
